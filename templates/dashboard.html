{% extends "base.html" %}

{% block title %}Dashboard - Librainian{% endblock %}



{% block content %}
<div class="dashboard-content fade-in">
    <!-- Welcome Section -->
    <div class="welcome-section mb-4">
        <div class="modern-card">
            <div class="modern-card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="welcome-title mb-2">
                            Welcome back, {{ user.get_full_name|default:user.username }}! 👋
                        </h2>
                        <p class="welcome-subtitle text-muted mb-0">
                            Here's what's happening with your library today.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="welcome-actions">
                            <button class="btn-primary-modern me-2" onclick="enableNotifications()">
                                <i class="fas fa-bell me-1"></i>
                                Enable Notifications
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid mb-4">
        <div class="row g-4">
            <!-- Card 1: Students This Month -->
            <div class="col-lg-3 col-md-6">
                <div class="stats-card slide-up" data-card="students-month">
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-value" id="students-month-value">
                            <span class="loading-placeholder">{{ students_this_month|default:0 }}</span>
                        </h3>
                        <p class="stats-label">Students This Month</p>
                        <div class="stats-trend">
                            {% if students_growth_percent != 0 %}
                                {% if students_growth_positive %}
                                <small class="text-success trend-positive">
                                    <i class="fas fa-arrow-up me-1"></i>
                                    <span class="trend-value">+{{ students_growth_percent|floatformat:1 }}%</span> from last month
                                </small>
                                {% else %}
                                <small class="text-danger trend-negative">
                                    <i class="fas fa-arrow-down me-1"></i>
                                    <span class="trend-value">{{ students_growth_percent|floatformat:1 }}%</span> from last month
                                </small>
                                {% endif %}
                            {% else %}
                            <small class="text-muted">
                                <i class="fas fa-minus me-1"></i>
                                No change from last month
                            </small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card 2: New Registrations -->
            <div class="col-lg-3 col-md-6">
                <div class="stats-card slide-up" style="animation-delay: 0.1s;" data-card="new-registrations">
                    <div class="stats-icon" style="background: linear-gradient(135deg, var(--secondary), #059669);">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-value" id="new-registrations-value">
                            <span class="loading-placeholder">{{ new_registrations_this_month|default:0 }}</span>
                        </h3>
                        <p class="stats-label">New Registrations</p>
                        <div class="stats-trend">
                            {% if registrations_growth_percent != 0 %}
                                {% if registrations_growth_positive %}
                                <small class="text-success trend-positive">
                                    <i class="fas fa-arrow-up me-1"></i>
                                    <span class="trend-value">+{{ registrations_growth_percent|floatformat:1 }}%</span> from last month
                                </small>
                                {% else %}
                                <small class="text-danger trend-negative">
                                    <i class="fas fa-arrow-down me-1"></i>
                                    <span class="trend-value">{{ registrations_growth_percent|floatformat:1 }}%</span> from last month
                                </small>
                                {% endif %}
                            {% else %}
                            <small class="text-muted">
                                <i class="fas fa-minus me-1"></i>
                                No change from last month
                            </small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card 3: Today's Collection -->
            <div class="col-lg-3 col-md-6">
                <div class="stats-card slide-up" style="animation-delay: 0.2s;" data-card="todays-collection">
                    <div class="stats-icon" style="background: linear-gradient(135deg, var(--warning), #d97706);">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-value" id="todays-collection-value">
                            <span class="loading-placeholder">₹{{ todays_collection|floatformat:0|default:0 }}</span>
                        </h3>
                        <p class="stats-label">Today's Collection</p>
                        <div class="stats-trend">
                            <small class="text-info">
                                <i class="fas fa-calendar me-1"></i>
                                <span class="collection-status">
                                    {% if todays_collection > 0 %}
                                        Collected today
                                    {% else %}
                                        No payments received today
                                    {% endif %}
                                </span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card 4: Monthly Revenue -->
            <div class="col-lg-3 col-md-6">
                <div class="stats-card slide-up" style="animation-delay: 0.3s;" data-card="monthly-revenue">
                    <div class="stats-icon" style="background: linear-gradient(135deg, var(--info), #0891b2);">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stats-content">
                        <h3 class="stats-value" id="monthly-revenue-value">
                            <span class="loading-placeholder">₹{{ total_invoice_amount|floatformat:0|default:0 }}</span>
                        </h3>
                        <p class="stats-label">Monthly Revenue</p>
                        <div class="stats-trend">
                            <small class="text-success">
                                <i class="fas fa-chart-line me-1"></i>
                                <span class="revenue-period">This month's total</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    {% if role != "sublibrarian" %}
    <div class="charts-section mb-4">
        <div class="row g-4">
            <div class="col-lg-6">
                <div class="modern-card">
                    <div class="modern-card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Monthly Revenue
                        </h5>
                    </div>
                    <div class="modern-card-body">
                        <canvas id="monthlySalesChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="modern-card">
                    <div class="modern-card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>
                            Student Growth
                        </h5>
                    </div>
                    <div class="modern-card-body">
                        <canvas id="studentGrowthChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Visitor Analytics Row -->
        <div class="row g-4 mt-2">
            <div class="col-lg-12">
                <div class="modern-card">
                    <div class="modern-card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-eye me-2"></i>
                            Visitor Analytics (Last 30 Days)
                        </h5>
                    </div>
                    <div class="modern-card-body">
                        <canvas id="visitorAnalyticsChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Data Tables Section -->
    <div class="tables-section">
        <div class="row g-4">
            {% if role != "sublibrarian" %}
            <div class="col-md-6">
                <div class="modern-card">
                    <div class="modern-card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            Students by Area
                        </h5>
                    </div>
                    <div class="modern-card-body">
                        <div class="modern-table">
                            <table class="table table-hover" id="areaTable">
                                <thead>
                                    <tr>
                                        <th>Area</th>
                                        <th>Students</th>
                                        <th>Growth</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in state_wise_student_count %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="area-indicator me-2"></div>
                                                {{ entry.locality }}
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ entry.count }}</span>
                                        </td>
                                        <td>
                                            <small class="text-success">
                                                <i class="fas fa-arrow-up"></i> +5%
                                            </small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <div class="col-md-6">
                <div class="modern-card">
                    <div class="modern-card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>
                            Recent Activities
                        </h5>
                    </div>
                    <div class="modern-card-body">
                        <div class="activity-list">
                            <div class="activity-item">
                                <div class="activity-icon bg-success">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <h6 class="activity-title">New Student Registration</h6>
                                    <p class="activity-desc">John Doe registered for Morning Shift</p>
                                    <small class="activity-time">2 minutes ago</small>
                                </div>
                            </div>
                            
                            <div class="activity-item">
                                <div class="activity-icon bg-primary">
                                    <i class="fas fa-rupee-sign"></i>
                                </div>
                                <div class="activity-content">
                                    <h6 class="activity-title">Payment Received</h6>
                                    <p class="activity-desc">₹2,500 payment from Jane Smith</p>
                                    <small class="activity-time">15 minutes ago</small>
                                </div>
                            </div>
                            
                            <div class="activity-item">
                                <div class="activity-icon bg-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="activity-content">
                                    <h6 class="activity-title">Pending Approval</h6>
                                    <p class="activity-desc">3 students waiting for approval</p>
                                    <small class="activity-time">1 hour ago</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions-section mt-4">
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="modern-card-body">
                <div class="row g-3">
                    <div class="col-md-3 col-sm-6">
                        <a href="/students/" class="quick-action-btn">
                            <i class="fas fa-users"></i>
                            <span>Manage Students</span>
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <a href="/students/temp_students_list/" class="quick-action-btn">
                            <i class="fas fa-clock"></i>
                            <span>Pending Approvals</span>
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <a href="/{{ role }}/daily-transaction/" class="quick-action-btn">
                            <i class="fas fa-money-bill"></i>
                            <span>Add Transaction</span>
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <a href="/membership/plans/" class="quick-action-btn">
                            <i class="fas fa-crown"></i>
                            <span>Upgrade Plan</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard Specific Overrides */
    .page-content {
        padding: 2rem;
    }

    /* Ensure proper mobile viewport handling */
    .dashboard-content {
        width: 100%;
        max-width: 100%;
    }

    /* Dashboard specific card styling - Make modern-card transparent like stats-card */
    .modern-card {
        background: rgba(255, 255, 255, 0.15) !important;
        backdrop-filter: blur(20px) !important;
        -webkit-backdrop-filter: blur(20px) !important;
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
        border-radius: 16px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.3s ease !important;
        position: relative;
        overflow: hidden;
    }

    .modern-card:hover {
        transform: translateY(-3px) !important;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2) !important;
        border-color: rgba(255, 255, 255, 0.4) !important;
    }

    .modern-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 100%);
        pointer-events: none;
        z-index: 1;
    }

    .modern-card-header {
        background: rgba(255, 255, 255, 0.1) !important;
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
        position: relative;
        z-index: 2;
    }

    .modern-card-header h5 {
        color: white !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .modern-card-body {
        padding: 2rem;
        position: relative;
        z-index: 2;
        background: transparent !important;
    }

    /* Ensure text content is visible in transparent cards */
    .modern-card .welcome-title,
    .modern-card h1, .modern-card h2, .modern-card h3,
    .modern-card h4, .modern-card h5, .modern-card h6 {
        color: white !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .modern-card p, .modern-card span, .modern-card div,
    .modern-card .text-muted, .modern-card .welcome-subtitle {
        color: rgba(255, 255, 255, 0.9) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .modern-card .table {
        color: rgba(255, 255, 255, 0.9) !important;
        background: transparent !important;
    }

    .modern-card .table th {
        color: white !important;
        background: transparent !important;
        border-color: rgba(255, 255, 255, 0.2) !important;
    }

    .modern-card .table td {
        color: rgba(255, 255, 255, 0.9) !important;
        background: transparent !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
    }

    .modern-card .table tbody tr {
        background: transparent !important;
    }

    .modern-card .table tbody tr:hover {
        background: rgba(255, 255, 255, 0.05) !important;
    }

    /* Specific styling for Students by Area table - Override Bootstrap defaults */
    #areaTable,
    #areaTable.table,
    .modern-table #areaTable {
        background: transparent !important;
        background-color: transparent !important;
    }

    #areaTable thead,
    #areaTable.table thead,
    .modern-table #areaTable thead {
        background: transparent !important;
        background-color: transparent !important;
    }

    #areaTable tbody,
    #areaTable.table tbody,
    .modern-table #areaTable tbody {
        background: transparent !important;
        background-color: transparent !important;
    }

    #areaTable tr,
    #areaTable.table tr,
    .modern-table #areaTable tr,
    #areaTable.table-hover tbody tr {
        background: transparent !important;
        background-color: transparent !important;
    }

    #areaTable th,
    #areaTable td,
    #areaTable.table th,
    #areaTable.table td,
    .modern-table #areaTable th,
    .modern-table #areaTable td {
        background: transparent !important;
        background-color: transparent !important;
    }

    /* Override Bootstrap table-hover effect */
    #areaTable.table-hover tbody tr:hover,
    .modern-table #areaTable.table-hover tbody tr:hover {
        background: rgba(255, 255, 255, 0.05) !important;
        background-color: rgba(255, 255, 255, 0.05) !important;
    }

    /* Make sure the modern-table container is also transparent */
    .modern-table {
        background: transparent !important;
        background-color: transparent !important;
    }

    .modern-card .activity-title {
        color: white !important;
    }

    .modern-card .activity-description {
        color: rgba(255, 255, 255, 0.8) !important;
    }

    .modern-card .activity-time {
        color: rgba(255, 255, 255, 0.7) !important;
    }

    .welcome-title {
        font-weight: 700;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .welcome-subtitle {
        color: rgba(255, 255, 255, 0.9);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary-modern:hover {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    /* Stats Cards */
    .stats-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        height: auto;
        min-height: 140px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 100%);
        pointer-events: none;
    }

    .stats-value {
        font-size: 2rem;
        font-weight: 800;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        margin: 0;
    }

    .stats-label {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 600;
        margin: 0;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        word-wrap: break-word;
        word-break: break-word;
        hyphens: auto;
        line-height: 1.3;
        font-size: 0.9rem;
    }

    .stats-trend {
        margin-top: 0.5rem;
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.875rem;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        flex-shrink: 0;
        margin-bottom: 1rem;
        align-self: flex-start;
    }

    /* Activity List */
    .activity-list {
        max-height: 400px;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    }

    .activity-list::-webkit-scrollbar {
        width: 6px;
    }

    .activity-list::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
    }

    .activity-list::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;
    }

    .activity-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 2;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.875rem;
        flex-shrink: 0;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-size: 0.875rem;
        font-weight: 600;
        margin: 0 0 0.25rem 0;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .activity-desc {
        font-size: 0.8125rem;
        color: rgba(255, 255, 255, 0.8);
        margin: 0 0 0.25rem 0;
    }

    .activity-time {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
    }

    /* Quick Actions */
    .quick-action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1.5rem 1rem;
        background: rgba(255, 255, 255, 0.15);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        text-decoration: none;
        color: white;
        transition: all 0.3s ease;
        text-align: center;
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        position: relative;
        overflow: hidden;
    }

    .quick-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 100%);
        pointer-events: none;
    }

    .quick-action-btn:hover {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    }

    .quick-action-btn i {
        font-size: 1.5rem;
        position: relative;
        z-index: 2;
    }

    .quick-action-btn span {
        font-weight: 600;
        font-size: 0.875rem;
        position: relative;
        z-index: 2;
    }

    /* Chart Containers */
    .chart-container {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        height: 400px;
        display: flex;
        flex-direction: column;
    }

    .chart-container canvas {
        flex: 1;
        max-height: 350px;
    }

    /* Uniform Card Heights */
    .row.g-4 .col-lg-8 .modern-card,
    .row.g-4 .col-lg-4 .modern-card {
        height: 400px;
        display: flex;
        flex-direction: column;
    }

    .row.g-4 .modern-card-body {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .activity-list {
        flex: 1;
        overflow-y: auto;
    }

    /* Tables */
    .table-glass {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        overflow: hidden;
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .table-glass th {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        font-weight: 600;
        border: none;
        padding: 1rem;
    }

    .table-glass td {
        color: rgba(255, 255, 255, 0.9);
        border-color: rgba(255, 255, 255, 0.1);
        padding: 1rem;
    }

    .table-glass tbody tr:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        /* Remove excessive margins and padding on mobile */
        .page-content {
            padding: 0.5rem !important;
        }

        .dashboard-content {
            padding: 0.5rem !important;
            margin: 0 !important;
        }

        /* Container adjustments */
        .container-fluid {
            padding-left: 0.5rem !important;
            padding-right: 0.5rem !important;
        }

        /* Welcome section mobile optimization */
        .welcome-section {
            margin-bottom: 1rem !important;
        }

        .modern-card {
            margin-left: 0 !important;
            margin-right: 0 !important;
            border-radius: 12px !important;
        }

        .modern-card-body {
            padding: 1rem !important;
        }

        .welcome-title {
            font-size: 1.5rem !important;
            margin-bottom: 0.5rem !important;
        }

        .welcome-subtitle {
            font-size: 0.875rem !important;
        }

        .welcome-actions {
            margin-top: 1rem;
            text-align: center !important;
        }

        .btn-primary-modern {
            padding: 0.625rem 1rem !important;
            font-size: 0.875rem !important;
            width: 100% !important;
            justify-content: center !important;
        }

        /* Stats cards mobile optimization */
        .stats-grid {
            margin-bottom: 1rem !important;
        }

        .stats-grid .row {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

        .stats-grid .col-lg-3,
        .stats-grid .col-md-6 {
            padding-left: 0.25rem !important;
            padding-right: 0.25rem !important;
            margin-bottom: 0.5rem !important;
        }

        .stats-card {
            height: auto !important;
            min-height: 140px !important;
            padding: 1rem !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: flex-start !important;
        }

        .stats-value {
            font-size: 1.5rem !important;
            margin: 0.5rem 0 !important;
        }

        .stats-label {
            font-size: 0.8rem !important;
            line-height: 1.2 !important;
            word-wrap: break-word !important;
            word-break: break-word !important;
            hyphens: auto !important;
            margin-top: 0.5rem !important;
            flex: 1 !important;
        }

        .stats-icon {
            width: 45px !important;
            height: 45px !important;
            font-size: 1.1rem !important;
            margin-bottom: 0.75rem !important;
            flex-shrink: 0 !important;
        }

        .stats-trend {
            font-size: 0.75rem !important;
        }

        /* Charts section mobile */
        .charts-section {
            margin-bottom: 1rem !important;
        }

        .charts-section .row {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

        .charts-section .col-lg-6 {
            padding-left: 0.25rem !important;
            padding-right: 0.25rem !important;
            margin-bottom: 1rem !important;
        }

        /* Tables section mobile */
        .tables-section .row {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

        .tables-section .col-md-6 {
            padding-left: 0.25rem !important;
            padding-right: 0.25rem !important;
            margin-bottom: 1rem !important;
        }

        /* Activity list mobile optimization */
        .activity-list {
            max-height: 300px !important;
        }

        .activity-item {
            padding: 0.75rem 0 !important;
        }

        .activity-icon {
            width: 35px !important;
            height: 35px !important;
            font-size: 0.8rem !important;
        }

        .activity-title {
            font-size: 0.8rem !important;
        }

        .activity-desc {
            font-size: 0.75rem !important;
        }

        .activity-time {
            font-size: 0.7rem !important;
        }

        /* Quick actions mobile */
        .quick-actions-section {
            margin-top: 1rem !important;
        }

        .quick-actions-section .row {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

        .quick-actions-section .col-md-3,
        .quick-actions-section .col-sm-6 {
            padding-left: 0.25rem !important;
            padding-right: 0.25rem !important;
            margin-bottom: 0.5rem !important;
        }

        .quick-action-btn {
            padding: 1rem 0.5rem !important;
            min-height: 80px !important;
        }

        .quick-action-btn i {
            font-size: 1.25rem !important;
        }

        .quick-action-btn span {
            font-size: 0.8rem !important;
        }

        /* Table responsive */
        .table-responsive {
            font-size: 0.8rem !important;
        }

        .modern-card .table th,
        .modern-card .table td {
            padding: 0.5rem !important;
            font-size: 0.8rem !important;
        }

        /* Modern card headers mobile */
        .modern-card-header {
            padding: 1rem !important;
        }

        .modern-card-header h5 {
            font-size: 1rem !important;
        }
    }

    @media (max-width: 576px) {
        /* Extra small devices */
        .page-content {
            padding: 0.25rem !important;
        }

        .dashboard-content {
            padding: 0.25rem !important;
        }

        .container-fluid {
            padding-left: 0.25rem !important;
            padding-right: 0.25rem !important;
        }

        .modern-card-body {
            padding: 0.75rem !important;
        }

        .welcome-title {
            font-size: 1.25rem !important;
        }

        .btn-primary-modern {
            padding: 0.5rem 0.75rem !important;
            font-size: 0.8rem !important;
        }

        .stats-card {
            padding: 0.75rem !important;
            min-height: 120px !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: flex-start !important;
        }

        .stats-value {
            font-size: 1.25rem !important;
            margin: 0.25rem 0 !important;
        }

        .stats-label {
            font-size: 0.75rem !important;
            line-height: 1.1 !important;
            word-wrap: break-word !important;
            word-break: break-word !important;
            hyphens: auto !important;
            margin-top: 0.25rem !important;
            flex: 1 !important;
            max-width: 7rem;
        }

        .stats-icon {
            width: 40px !important;
            height: 40px !important;
            font-size: 1rem !important;
            margin-bottom: 0.5rem !important;
            flex-shrink: 0 !important;
        }

        .quick-action-btn {
            padding: 0.75rem 0.5rem !important;
            min-height: 70px !important;
        }

        .quick-action-btn i {
            font-size: 1.1rem !important;
        }

        .quick-action-btn span {
            font-size: 0.75rem !important;
        }

        /* Grid adjustments for very small screens */
        .stats-grid .col-lg-3,
        .stats-grid .col-md-6 {
            flex: 0 0 50% !important;
            max-width: 50% !important;
        }

        .quick-actions-section .col-md-3,
        .quick-actions-section .col-sm-6 {
            flex: 0 0 50% !important;
            max-width: 50% !important;
        }
    }

    /* Landscape mobile optimization */
    @media (max-width: 768px) and (orientation: landscape) {
        .stats-grid .col-lg-3,
        .stats-grid .col-md-6 {
            flex: 0 0 25% !important;
            max-width: 25% !important;
        }

        .quick-actions-section .col-md-3,
        .quick-actions-section .col-sm-6 {
            flex: 0 0 25% !important;
            max-width: 25% !important;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<!-- Dashboard Testing Script (Development Only) -->
{% if debug %}
<script src="{% load static %}{% static 'js/dashboard-test.js' %}"></script>
{% endif %}
<script>
    // Enhanced Dashboard functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Add loading states to cards
        addLoadingStates();

        // Initialize charts if Chart.js is available
        if (typeof Chart !== 'undefined') {
            setTimeout(() => {
                initializeCharts();
                removeLoadingStates();
            }, 500);
        }

        // Initialize data tables if jQuery and DataTables are available
        if (typeof $ !== 'undefined' && $.fn.DataTable) {
            initializeDataTables();
        }

        // Add card interactions
        addCardInteractions();
    });

    function addLoadingStates() {
        const cards = document.querySelectorAll('.stats-card');
        cards.forEach(card => {
            card.classList.add('loading');
        });
    }

    function removeLoadingStates() {
        const cards = document.querySelectorAll('.stats-card');
        cards.forEach(card => {
            card.classList.remove('loading');
        });
    }

    function addCardInteractions() {
        const cards = document.querySelectorAll('.stats-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    function initializeCharts() {
        // Monthly Revenue Chart
        const salesCtx = document.getElementById('monthlySalesChart');
        if (salesCtx) {
            try {
                // Real revenue data from backend
                const salesMonths = {{ revenue_months|safe|default:"['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']" }};
                const salesAmounts = {{ revenue_amounts|safe|default:"[12000, 19000, 15000, 25000, 22000, 30000]" }};

                // Format currency for tooltips
                const formatCurrency = (value) => {
                    return '₹' + value.toLocaleString('en-IN');
                };

                new Chart(salesCtx, {
                    type: 'bar',
                    data: {
                        labels: salesMonths,
                        datasets: [{
                            label: 'Monthly Revenue (₹)',
                            data: salesAmounts,
                            backgroundColor: 'rgba(34, 211, 238, 0.6)',
                            borderColor: 'rgba(34, 211, 238, 1)',
                            borderWidth: 2,
                            borderRadius: 6,
                            hoverBackgroundColor: 'rgba(34, 211, 238, 0.8)',
                            barPercentage: 0.7,
                            categoryPercentage: 0.8
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                labels: {
                                    color: 'rgba(255, 255, 255, 0.9)',
                                    font: { size: 14 }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: 'rgba(255, 255, 255, 0.9)',
                                bodyColor: 'rgba(255, 255, 255, 0.8)',
                                borderColor: 'rgba(34, 211, 238, 0.5)',
                                borderWidth: 1,
                                callbacks: {
                                    label: function(context) {
                                        return context.dataset.label + ': ' + formatCurrency(context.parsed.y);
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: {
                                    color: 'rgba(255, 255, 255, 0.8)',
                                    callback: function(value) {
                                        return '₹' + value.toLocaleString();
                                    }
                                }
                            },
                            x: {
                                grid: { display: false },
                                ticks: { color: 'rgba(255, 255, 255, 0.8)' }
                            }
                        }
                    }
                });
            } catch (error) {
                // Sales chart initialization failed - show fallback
                salesCtx.parentElement.innerHTML = '<div class="text-center text-white p-4"><i class="fas fa-chart-line fa-3x mb-3 opacity-50"></i><p>Chart data loading...</p></div>';
            }
        }

        // Student Growth Chart
        const growthCtx = document.getElementById('studentGrowthChart');
        if (growthCtx) {
            try {
                // Real data from backend
                const growthMonths = {{ student_growth_months|safe|default:"['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']" }};
                const growthData = {{ student_growth_counts|safe|default:"[45, 52, 38, 67, 73, 89]" }};

                new Chart(growthCtx, {
                    type: 'bar',
                    data: {
                        labels: growthMonths,
                        datasets: [{
                            label: 'New Students',
                            data: growthData,
                            backgroundColor: 'rgba(34, 197, 94, 0.6)',
                            borderColor: 'rgba(34, 197, 94, 1)',
                            borderWidth: 2,
                            borderRadius: 6,
                            hoverBackgroundColor: 'rgba(34, 197, 94, 0.8)',
                            barPercentage: 0.7,
                            categoryPercentage: 0.8
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                labels: {
                                    color: 'rgba(255, 255, 255, 0.9)',
                                    font: { size: 14 }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                ticks: { color: 'rgba(255, 255, 255, 0.8)' }
                            },
                            x: {
                                grid: { display: false },
                                ticks: { color: 'rgba(255, 255, 255, 0.8)' }
                            }
                        }
                    }
                });
            } catch (error) {
                // Growth chart initialization failed - show fallback
                growthCtx.parentElement.innerHTML = '<div class="text-center text-white p-4"><i class="fas fa-chart-bar fa-3x mb-3 opacity-50"></i><p>Chart data loading...</p></div>';
            }
        }

        // Visitor Analytics Chart
        const visitorCtx = document.getElementById('visitorAnalyticsChart');
        if (visitorCtx) {
            try {
                // Real visitor data from backend
                const visitorDays = {{ visitor_days|safe|default:"['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5']" }};
                const visitorCounts = {{ visitor_counts|safe|default:"[12, 19, 15, 25, 22]" }};

                new Chart(visitorCtx, {
                    type: 'line',
                    data: {
                        labels: visitorDays,
                        datasets: [{
                            label: 'Daily Visitors',
                            data: visitorCounts,
                            borderColor: 'rgba(139, 92, 246, 0.9)',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: 'rgba(139, 92, 246, 0.9)',
                            pointBorderColor: 'rgba(139, 92, 246, 1)',
                            pointBorderWidth: 2,
                            pointRadius: 6,
                            pointHoverRadius: 8
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                labels: {
                                    color: 'rgba(255, 255, 255, 0.9)',
                                    font: { size: 14 }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: 'rgba(255, 255, 255, 0.9)',
                                bodyColor: 'rgba(255, 255, 255, 0.8)',
                                borderColor: 'rgba(139, 92, 246, 0.5)',
                                borderWidth: 1
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)',
                                    drawBorder: false
                                },
                                ticks: {
                                    color: 'rgba(255, 255, 255, 0.8)',
                                    stepSize: 1
                                }
                            },
                            x: {
                                grid: { display: false },
                                ticks: { color: 'rgba(255, 255, 255, 0.8)' }
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        }
                    }
                });
            } catch (error) {
                // Visitor chart initialization failed - show fallback
                visitorCtx.parentElement.innerHTML = '<div class="text-center text-white p-4"><i class="fas fa-eye fa-3x mb-3 opacity-50"></i><p>Visitor data loading...</p></div>';
            }
        }
    }

    function initializeDataTables() {
        const areaTable = document.getElementById('areaTable');
        if (areaTable) {
            try {
                $(areaTable).DataTable({
                    pageLength: 5,
                    lengthChange: false,
                    searching: false,
                    info: false,
                    ordering: true,
                    responsive: true
                });
            } catch (error) {
                // DataTable initialization failed - silent fallback
            }
        }
    }

    // Notification functionality
    function enableNotifications() {
        const button = event.target;
        const originalText = button.innerHTML;

        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Enabling...';
        button.disabled = true;

        if ('Notification' in window) {
            Notification.requestPermission().then(function(permission) {
                if (permission === 'granted') {
                    // Show browser notification
                    new Notification('Notifications Enabled!', {
                        body: 'You will now receive notifications from the dashboard.',
                        icon: '/static/img/librainian-logo-black-transparent.png'
                    });

                    // Update button
                    button.innerHTML = '<i class="fas fa-check me-1"></i>Enabled!';
                    button.style.background = 'linear-gradient(135deg, rgba(16, 185, 129, 0.3) 0%, rgba(16, 185, 129, 0.2) 100%)';
                    button.style.borderColor = 'rgba(16, 185, 129, 0.5)';

                    // Show toast
                    if (window.modernDashboard) {
                        window.modernDashboard.showToast(
                            'Success!',
                            'Notifications enabled successfully.',
                            'success'
                        );
                    }

                    // Reset button after 3 seconds
                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.disabled = false;
                        button.style.background = '';
                        button.style.borderColor = '';
                    }, 3000);

                } else {
                    button.innerHTML = originalText;
                    button.disabled = false;

                    if (window.modernDashboard) {
                        window.modernDashboard.showToast(
                            'Permission Denied',
                            'Please allow notifications in your browser settings.',
                            'warning'
                        );
                    }
                }
            }).catch(error => {
                button.innerHTML = originalText;
                button.disabled = false;

                if (window.modernDashboard) {
                    window.modernDashboard.showToast(
                        'Error',
                        'Failed to enable notifications.',
                        'danger'
                    );
                }
            });
        } else {
            button.innerHTML = originalText;
            button.disabled = false;

            if (window.modernDashboard) {
                window.modernDashboard.showToast(
                    'Not Supported',
                    'Notifications are not supported in this browser.',
                    'danger'
                );
            }
        }
    }
</script>
{% endblock %}
