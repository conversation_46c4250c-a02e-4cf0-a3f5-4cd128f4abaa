<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Invoice Generated - {{ library_name|default:'Librainian' }}</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    /* Only essential CSS kept; table styles removed */
    body {
      font-family: Arial, sans-serif;
      background-color: #f9fafb;
      padding: 20px;
      color: #1f2937;
    }

    .invoice-header {
      background: linear-gradient(135deg, #059669 0%, #047857 100%);
      color: white;
      padding: 25px;
      border-radius: 12px;
      margin: 20px 0;
      text-align: center;
    }

    .invoice-number {
      font-size: 24px;
      font-weight: 600;
      font-family: 'Comfortaa', sans-serif;
    }

    .invoice-date {
      font-size: 16px;
      opacity: 0.9;
    }

    .amount-section {
      background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
      border: 2px solid #22c55e;
      border-radius: 16px;
      padding: 30px;
      text-align: center;
      margin: 25px 0;
    }

    .amount-label {
      font-size: 16px;
      color: #166534;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .amount-value {
      font-size: 42px;
      font-weight: 800;
      color: #059669;
      font-family: 'Comfortaa', sans-serif;
      text-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
    }

    .payment-status {
      display: inline-block;
      background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
      color: white;
      padding: 12px 20px;
      border-radius: 25px;
      font-weight: 600;
      font-size: 14px;
      margin: 15px 0;
    }

    .download-section {
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      border: 1px solid #f59e0b;
      border-radius: 12px;
      padding: 25px;
      text-align: center;
      margin: 25px 0;
    }

    .download-title {
      font-size: 18px;
      font-weight: 600;
      color: #92400e;
      font-family: 'Comfortaa', sans-serif;
      margin-bottom: 10px;
    }

    .download-button {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      color: white !important;
      padding: 12px 24px;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 14px 0 rgba(245, 158, 11, 0.3);
    }

    .download-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px 0 rgba(245, 158, 11, 0.4);
    }

    @media only screen and (max-width: 600px) {
      .amount-value {
        font-size: 32px;
      }

      .download-button {
        font-size: 13px;
        padding: 10px 20px;
      }
    }
  </style>
</head>
<body>

  <!-- Hidden preview text for email clients -->
  <div style="display:none; font-size:1px; color:#f9fafb; line-height:1px; max-height:0px; max-width:0px; opacity:0; overflow:hidden;">
    Invoice #{{ invoice_number }} for ₹{{ total_amount }}. Payment successful.
  </div>

  <!-- Email Body -->
  <h2>Hello {{ customer_name|default:'Valued Customer' }},</h2>
  <p>Thank you for your payment! Your invoice has been generated successfully.</p>

  <!-- Invoice Header -->
  <div class="invoice-header">
    <h3 class="invoice-number">Invoice #{{ invoice_number }}</h3>
    <p class="invoice-date">Generated on {{ invoice_date|date:"F j, Y" }}</p>
  </div>

  <!-- Amount Section -->
  <div class="amount-section">
    <p class="amount-label">Total Amount Paid</p>
    <p class="amount-value">₹{{ total_amount }}</p>
    <div class="payment-status">✅ Payment Successful</div>
  </div>

  <!-- View Invoice Section -->
  <div class="download-section">
    <h3 class="download-title">📄 View Your Invoice</h3>
    <a href="{{ invoice_download_url|default:'#' }}" class="download-button">
      🔍 View Invoice
    </a>
  </div>

  <!-- Footer -->
  <p style="margin-top: 30px; color: #6b7280; font-size: 14px; text-align: center;">
    Thank you for choosing Librainian! If you have any questions about this invoice, please contact our support team.
  </p>

</body>
</html>