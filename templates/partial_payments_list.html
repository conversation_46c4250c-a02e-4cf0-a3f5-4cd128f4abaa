{% extends "base.html" %}
{% load static %}

{% block title %}Partial Payments - {{ librarian.library_name }}{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: #667eea;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6b7280;
        font-weight: 500;
    }

    .payments-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .filters-section {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .filter-input {
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 10px;
        font-size: 1rem;
        min-width: 200px;
    }

    .filter-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* Desktop Table View */
    .payments-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;
    }

    .payments-table th,
    .payments-table td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
    }

    .payments-table th {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        font-weight: 600;
        color: #374151;
        position: sticky;
        top: 0;
    }

    .payments-table tr:hover {
        background-color: rgba(102, 126, 234, 0.05);
    }

    /* Mobile Card View */
    .payments-cards {
        display: none;
        gap: 1rem;
    }

    .payment-card {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid #e5e7eb;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }

    .payment-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .student-name {
        font-weight: 600;
        color: #1f2937;
        font-size: 1.1rem;
    }

    .invoice-id {
        color: #6b7280;
        font-size: 0.875rem;
    }

    .card-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
    }

    .detail-label {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 0.25rem;
    }

    .detail-value {
        font-weight: 500;
        color: #1f2937;
    }

    .progress-section {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
    }

    .progress-bar-container {
        background-color: #f3f4f6;
        border-radius: 10px;
        height: 8px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #667eea, #764ba2);
        transition: width 0.3s ease;
    }

    .progress-text {
        font-size: 0.875rem;
        color: #6b7280;
        text-align: center;
    }

    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-partial {
        background-color: #fef3c7;
        color: #d97706;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-outline {
        background: transparent;
        color: #667eea;
        border: 1px solid #667eea;
    }

    .btn-outline:hover {
        background: #667eea;
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #d1d5db;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .payments-table {
            display: none;
        }

        .payments-cards {
            display: flex;
            flex-direction: column;
        }

        .filters-section {
            flex-direction: column;
        }

        .filter-input {
            min-width: 100%;
        }

        .stats-cards {
            grid-template-columns: 1fr;
        }

        .card-details {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <h1><i class="fas fa-coins"></i> Partial Payments Management</h1>
        <p>Track and manage students with outstanding payment balances</p>
    </div>
</div>

<div class="container">
    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-value">{{ invoice_data|length }}</div>
            <div class="stat-label">Students with Partial Payments</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-rupee-sign"></i>
            </div>
            <div class="stat-value">
                ₹{% for item in invoice_data %}{{ item.invoice.remaining_due }}{% if not forloop.last %}+{% endif %}{% endfor %}
            </div>
            <div class="stat-label">Total Outstanding</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-value">
                {% for item in invoice_data %}{{ item.payment_percentage }}{% if not forloop.last %}+{% endif %}{% endfor %}%
            </div>
            <div class="stat-label">Average Payment Progress</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="payments-container">
        <!-- Filters -->
        <div class="filters-section">
            <input type="text" id="searchInput" class="filter-input" placeholder="🔍 Search by student name or invoice ID...">
            <select id="sortSelect" class="filter-input">
                <option value="name">Sort by Name</option>
                <option value="amount">Sort by Outstanding Amount</option>
                <option value="date">Sort by Last Payment Date</option>
                <option value="progress">Sort by Payment Progress</option>
            </select>
        </div>

        {% if invoice_data %}
        <!-- Desktop Table View -->
        <table class="payments-table" id="paymentsTable">
            <thead>
                <tr>
                    <th>Student</th>
                    <th>Invoice ID</th>
                    <th>Total Amount</th>
                    <th>Paid Amount</th>
                    <th>Outstanding</th>
                    <th>Progress</th>
                    <th>Last Payment</th>
                    <th>Next Due</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for item in invoice_data %}
                <tr class="payment-row" data-student-name="{{ item.student.name|lower }}" data-invoice-id="{{ item.invoice.invoice_id|lower }}">
                    <td>
                        <div>
                            <strong>{{ item.student.name }}</strong><br>
                            <small class="text-muted">{{ item.student.course.name|default:"N/A" }}</small>
                        </div>
                    </td>
                    <td>
                        <span class="invoice-id">#{{ item.invoice.invoice_id }}</span>
                    </td>
                    <td>₹{{ item.invoice.total_amount }}</td>
                    <td>₹{{ item.invoice.total_paid }}</td>
                    <td><strong>₹{{ item.invoice.remaining_due }}</strong></td>
                    <td>
                        <div class="progress-bar-container">
                            <div class="progress-bar" style="width: {{ item.payment_percentage }}%"></div>
                        </div>
                        <div class="progress-text">{{ item.payment_percentage }}% paid</div>
                    </td>
                    <td>{{ item.last_payment_date|default:"No payments" }}</td>
                    <td>{{ item.next_commitment_date|default:"Not set" }}</td>
                    <td>
                        <div class="action-buttons">
                            <a href="{% url 'invoice_student' item.invoice.slug %}" class="btn-sm btn-primary">
                                <i class="fas fa-eye"></i> View
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Mobile Card View -->
        <div class="payments-cards" id="paymentsCards">
            {% for item in invoice_data %}
            <div class="payment-card" data-student-name="{{ item.student.name|lower }}" data-invoice-id="{{ item.invoice.invoice_id|lower }}">
                <div class="card-header">
                    <div>
                        <div class="student-name">{{ item.student.name }}</div>
                        <div class="invoice-id">#{{ item.invoice.invoice_id }}</div>
                    </div>
                    <span class="status-badge status-partial">Partial</span>
                </div>
                
                <div class="card-details">
                    <div class="detail-item">
                        <span class="detail-label">Total Amount</span>
                        <span class="detail-value">₹{{ item.invoice.total_amount }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Paid Amount</span>
                        <span class="detail-value">₹{{ item.invoice.total_paid }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Outstanding</span>
                        <span class="detail-value"><strong>₹{{ item.invoice.remaining_due }}</strong></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Last Payment</span>
                        <span class="detail-value">{{ item.last_payment_date|default:"No payments" }}</span>
                    </div>
                </div>

                <div class="progress-section">
                    <div class="progress-bar-container">
                        <div class="progress-bar" style="width: {{ item.payment_percentage }}%"></div>
                    </div>
                    <div class="progress-text">{{ item.payment_percentage }}% paid</div>
                </div>

                <div class="action-buttons">
                    <a href="{% url 'invoice_student' item.invoice.slug %}" class="btn-sm btn-primary">
                        <i class="fas fa-eye"></i> View Invoice
                    </a>
                    <a href="{% url 'student_details' item.student.slug %}" class="btn-sm btn-outline">
                        <i class="fas fa-user"></i> Student Profile
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-check-circle"></i>
            <h3>No Partial Payments Found</h3>
            <p>All invoices are either fully paid or unpaid. Great job managing payments!</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Search and Filter Functionality
    class PartialPaymentsManager {
        constructor() {
            this.searchInput = document.getElementById('searchInput');
            this.sortSelect = document.getElementById('sortSelect');
            this.tableRows = document.querySelectorAll('.payment-row');
            this.cardElements = document.querySelectorAll('.payment-card');
            this.init();
        }

        init() {
            this.setupEventListeners();
        }

        setupEventListeners() {
            if (this.searchInput) {
                this.searchInput.addEventListener('input', () => this.filterPayments());
            }

            if (this.sortSelect) {
                this.sortSelect.addEventListener('change', () => this.sortPayments());
            }
        }

        filterPayments() {
            const searchTerm = this.searchInput.value.toLowerCase();

            // Filter table rows
            this.tableRows.forEach(row => {
                const studentName = row.dataset.studentName;
                const invoiceId = row.dataset.invoiceId;
                const isVisible = studentName.includes(searchTerm) || invoiceId.includes(searchTerm);
                row.style.display = isVisible ? '' : 'none';
            });

            // Filter cards
            this.cardElements.forEach(card => {
                const studentName = card.dataset.studentName;
                const invoiceId = card.dataset.invoiceId;
                const isVisible = studentName.includes(searchTerm) || invoiceId.includes(searchTerm);
                card.style.display = isVisible ? '' : 'none';
            });
        }

        sortPayments() {
            const sortBy = this.sortSelect.value;
            // Implementation for sorting would go here
            // For now, we'll just show a message
            console.log(`Sorting by: ${sortBy}`);
        }
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
        new PartialPaymentsManager();
    });
</script>
{% endblock %}
