{% extends "email_base.html" %}

{% block email_title %}Payment Reminder - {{ library_name|default:'Librainian' }}{% endblock %}

{% block email_subject %}Payment Reminder - Invoice #{{ invoice.invoice_id }}{% endblock %}

{% block email_description %}Payment reminder for outstanding balance on library services subscription.{% endblock %}

{% block preview_text %}Friendly reminder: You have an outstanding balance of ₹{{ invoice.remaining_due }} on invoice #{{ invoice.invoice_id }}.{% endblock %}

{% block header_icon %}⏰{% endblock %}

{% block email_header_title %}Payment Reminder{% endblock %}

{% block email_content %}
<h2 class="greeting">Hello {{ student.name }},</h2>
<p class="message">
    We hope you're enjoying our library services! This is a friendly reminder about your outstanding payment balance.
</p>

<!-- Reminder Alert -->
<div class="reminder-alert">
    <div class="alert-icon">
        <i class="fas fa-exclamation-triangle"></i>
    </div>
    <div class="alert-content">
        <h3 class="alert-title">Payment Reminder</h3>
        <p class="alert-message">
            Your invoice #{{ invoice.invoice_id }} has an outstanding balance of 
            <strong>₹{{ invoice.remaining_due }}</strong> that was due {{ days_overdue }} days ago.
        </p>
    </div>
</div>

<!-- Payment Status -->
<div class="payment-status">
    <h3 class="section-title">💳 Payment Status</h3>
    
    <div class="status-grid">
        <div class="status-item">
            <span class="status-label">Invoice Number:</span>
            <span class="status-value">#{{ invoice.invoice_id }}</span>
        </div>
        
        <div class="status-item">
            <span class="status-label">Total Amount:</span>
            <span class="status-value">₹{{ invoice.total_amount }}</span>
        </div>
        
        <div class="status-item">
            <span class="status-label">Amount Paid:</span>
            <span class="status-value paid">₹{{ invoice.total_paid }}</span>
        </div>
        
        <div class="status-item highlight">
            <span class="status-label">Outstanding Balance:</span>
            <span class="status-value outstanding">₹{{ invoice.remaining_due }}</span>
        </div>
        
        <div class="status-item">
            <span class="status-label">Due Date:</span>
            <span class="status-value">{{ invoice.due_date|date:"F j, Y" }}</span>
        </div>
        
        <div class="status-item">
            <span class="status-label">Days Overdue:</span>
            <span class="status-value overdue">{{ days_overdue }} days</span>
        </div>
    </div>
</div>

<!-- Payment Progress -->
<div class="payment-progress">
    <h3 class="section-title">📊 Payment Progress</h3>
    <div class="progress-container">
        <div class="progress-bar">
            <div class="progress-fill" style="width: {{ invoice.get_payment_percentage }}%"></div>
        </div>
        <div class="progress-text">
            {{ invoice.get_payment_percentage }}% paid (₹{{ invoice.total_paid }} of ₹{{ invoice.total_amount }})
        </div>
    </div>
</div>

<!-- Payment History -->
{% if payments %}
<div class="payment-history">
    <h3 class="section-title">📋 Payment History</h3>
    <div class="history-list">
        {% for payment in payments %}
        <div class="history-item">
            <div class="history-date">{{ payment.payment_date|date:"M j, Y" }}</div>
            <div class="history-amount">₹{{ payment.amount_paid }}</div>
            <div class="history-mode">{{ payment.payment_mode }}</div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<!-- Payment Options -->
<div class="payment-options">
    <h3 class="section-title">💰 How to Pay</h3>
    <div class="options-grid">
        <div class="option-card">
            <div class="option-icon">💳</div>
            <h4>Online Payment</h4>
            <p>Pay securely online using UPI, cards, or net banking</p>
        </div>
        
        <div class="option-card">
            <div class="option-icon">🏦</div>
            <h4>Bank Transfer</h4>
            <p>Transfer directly to our bank account</p>
        </div>
        
        <div class="option-card">
            <div class="option-icon">🏢</div>
            <h4>Visit Library</h4>
            <p>Pay in person at our library counter</p>
        </div>
        
        <div class="option-card">
            <div class="option-icon">📱</div>
            <h4>UPI Payment</h4>
            <p>Quick payment using any UPI app</p>
        </div>
    </div>
</div>

<!-- Contact Information -->
<div class="contact-section">
    <h3 class="section-title">📞 Need Help?</h3>
    <p>If you have any questions about your payment or need assistance, please contact us:</p>
    <div class="contact-details">
        <div class="contact-item">
            <i class="fas fa-envelope"></i>
            <span><EMAIL></span>
        </div>
        <div class="contact-item">
            <i class="fas fa-phone"></i>
            <span>+91-XXXXXXXXXX</span>
        </div>
        <div class="contact-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>{{ library_name }}</span>
        </div>
    </div>
</div>

<!-- Action Button -->
<div class="action-section">
    <a href="#" class="pay-now-button">
        <i class="fas fa-credit-card"></i>
        Pay Now - ₹{{ invoice.remaining_due }}
    </a>
    <p class="action-note">Click above to make your payment securely online</p>
</div>

<p class="closing-message">
    Thank you for your prompt attention to this matter. We appreciate your business and look forward to continuing to serve you!
</p>
{% endblock %}

{% block extra_css %}
<style>
    .reminder-alert {
        background: linear-gradient(135deg, #fef3c7, #fde68a);
        border: 2px solid #f59e0b;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .alert-icon {
        font-size: 2.5rem;
        color: #d97706;
    }

    .alert-content {
        flex: 1;
    }

    .alert-title {
        color: #92400e;
        margin: 0 0 0.5rem 0;
        font-size: 1.25rem;
        font-weight: 700;
    }

    .alert-message {
        color: #78350f;
        margin: 0;
        font-weight: 500;
    }

    .payment-status {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .status-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        background: white;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
    }

    .status-item.highlight {
        background: linear-gradient(135deg, #fee2e2, #fecaca);
        border-color: #ef4444;
    }

    .status-label {
        font-weight: 500;
        color: #6b7280;
    }

    .status-value {
        font-weight: 600;
        color: #1f2937;
    }

    .status-value.paid {
        color: #059669;
    }

    .status-value.outstanding {
        color: #dc2626;
        font-size: 1.1rem;
    }

    .status-value.overdue {
        color: #dc2626;
        font-weight: 700;
    }

    .payment-progress {
        background: #f1f5f9;
        border: 1px solid #cbd5e1;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .progress-container {
        margin-top: 1rem;
    }

    .progress-bar {
        width: 100%;
        height: 12px;
        background-color: #e5e7eb;
        border-radius: 6px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #10b981, #059669);
        transition: width 0.3s ease;
    }

    .progress-text {
        text-align: center;
        font-weight: 500;
        color: #374151;
    }

    .payment-history {
        background: #fefefe;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .history-list {
        margin-top: 1rem;
    }

    .history-item {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 1rem;
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
        align-items: center;
    }

    .history-item:last-child {
        border-bottom: none;
    }

    .history-date {
        font-weight: 500;
        color: #6b7280;
    }

    .history-amount {
        font-weight: 600;
        color: #059669;
        text-align: center;
    }

    .history-mode {
        font-weight: 500;
        color: #374151;
        text-align: right;
    }

    .payment-options {
        background: linear-gradient(135deg, #dbeafe, #bfdbfe);
        border: 1px solid #3b82f6;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .options-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-top: 1rem;
    }

    .option-card {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
        border: 1px solid #e5e7eb;
    }

    .option-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .option-card h4 {
        margin: 0.5rem 0;
        color: #1f2937;
        font-size: 1rem;
    }

    .option-card p {
        margin: 0;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .contact-section {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .contact-details {
        margin-top: 1rem;
    }

    .contact-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin: 0.75rem 0;
        color: #374151;
    }

    .contact-item i {
        color: #6366f1;
        width: 20px;
    }

    .action-section {
        text-align: center;
        margin: 2rem 0;
    }

    .pay-now-button {
        display: inline-block;
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 1rem 2rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        font-size: 1.1rem;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        transition: all 0.3s ease;
    }

    .pay-now-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        color: white;
        text-decoration: none;
    }

    .action-note {
        margin-top: 1rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .closing-message {
        text-align: center;
        font-style: italic;
        color: #6b7280;
        margin-top: 2rem;
        padding: 1rem;
        background: #f9fafb;
        border-radius: 8px;
    }

    @media only screen and (max-width: 600px) {
        .status-grid,
        .options-grid {
            grid-template-columns: 1fr;
        }
        
        .reminder-alert {
            flex-direction: column;
            text-align: center;
        }
        
        .history-item {
            grid-template-columns: 1fr;
            text-align: center;
        }
    }
</style>
{% endblock %}
