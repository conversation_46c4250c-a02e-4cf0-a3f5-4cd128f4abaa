{% extends "email_base.html" %}

{% block email_title %}Payment Reminder - {{ library_name|default:'Librainian' }}{% endblock %}

{% block email_subject %}Payment Reminder - Invoice #{{ invoice.invoice_id }}{% endblock %}

{% block email_description %}Payment reminder for outstanding balance on library services subscription.{% endblock %}

{% block preview_text %}Friendly reminder: You have an outstanding balance of ₹{{ invoice.remaining_due }} on invoice #{{ invoice.invoice_id }}.{% endblock %}

{% block header_icon %}⏰{% endblock %}

{% block email_header_title %}Payment Reminder{% endblock %}

{% block email_content %}
<h2 class="greeting">Hello {{ student.name }},</h2>
<p class="message">
    We hope you're enjoying our library services! This is a friendly reminder about your outstanding payment balance.
</p>

<!-- Reminder Alert -->
<div class="reminder-alert">
    <div class="alert-icon">
        <i class="fas fa-exclamation-triangle"></i>
    </div>
    <div class="alert-content">
        <h3 class="alert-title">Payment Reminder</h3>
        <p class="alert-message">
            Your invoice #{{ invoice.invoice_id }} has an outstanding balance of
            <strong>₹{{ invoice.remaining_due }}</strong> that was due {{ days_overdue }} days ago.
        </p>
    </div>
</div>

<!-- Payment Highlight Schema -->
<div class="payment-highlight-schema">
    <h3 class="schema-title">
        <i class="fas fa-clipboard-list"></i> Payment Summary
    </h3>

    <div class="highlight-grid">
        <div class="highlight-card pending-payment">
            <div class="card-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="card-content">
                <h4 class="card-title">Pending Payment</h4>
                <div class="card-value">₹{{ invoice.remaining_due }}</div>
                <div class="card-subtitle">Outstanding Balance</div>
            </div>
        </div>

        <div class="highlight-card amount-due">
            <div class="card-icon">
                <i class="fas fa-rupee-sign"></i>
            </div>
            <div class="card-content">
                <h4 class="card-title">Total Amount</h4>
                <div class="card-value">₹{{ invoice.total_amount }}</div>
                <div class="card-subtitle">Invoice Total</div>
            </div>
        </div>

        <div class="highlight-card months-covered">
            <div class="card-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="card-content">
                <h4 class="card-title">Service Period</h4>
                <div class="card-value">
                    {% for month in invoice.months.all %}
                        {{ month.name }}{% if not forloop.last %}, {% endif %}
                    {% endfor %}
                </div>
                <div class="card-subtitle">Months Covered</div>
            </div>
        </div>

        <div class="highlight-card due-date">
            <div class="card-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="card-content">
                <h4 class="card-title">Due Date</h4>
                <div class="card-value">{{ invoice.due_date|date:"d M Y" }}</div>
                <div class="card-subtitle">
                    {% if days_overdue > 0 %}
                        <span class="overdue-text">{{ days_overdue }} days overdue</span>
                    {% else %}
                        Payment Due
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Payment Breakdown -->
    <div class="payment-breakdown-summary">
        <div class="breakdown-row">
            <span class="breakdown-label">Total Invoice Amount:</span>
            <span class="breakdown-value">₹{{ invoice.total_amount }}</span>
        </div>
        {% if invoice.total_paid > 0 %}
        <div class="breakdown-row paid">
            <span class="breakdown-label">Amount Already Paid:</span>
            <span class="breakdown-value">₹{{ invoice.total_paid }}</span>
        </div>
        {% endif %}
        <div class="breakdown-row pending">
            <span class="breakdown-label">Pending Payment:</span>
            <span class="breakdown-value highlight">₹{{ invoice.remaining_due }}</span>
        </div>
        {% if invoice.total_paid > 0 %}
        <div class="breakdown-row progress">
            <span class="breakdown-label">Payment Progress:</span>
            <span class="breakdown-value">{{ invoice.get_payment_percentage }}% completed</span>
        </div>
        {% endif %}
    </div>
</div>

<!-- Payment Status -->
<div class="payment-status">
    <h3 class="section-title">💳 Payment Status</h3>
    
    <div class="status-grid">
        <div class="status-item">
            <span class="status-label">Invoice Number:</span>
            <span class="status-value">#{{ invoice.invoice_id }}</span>
        </div>
        
        <div class="status-item">
            <span class="status-label">Total Amount:</span>
            <span class="status-value">₹{{ invoice.total_amount }}</span>
        </div>
        
        <div class="status-item">
            <span class="status-label">Amount Paid:</span>
            <span class="status-value paid">₹{{ invoice.total_paid }}</span>
        </div>
        
        <div class="status-item highlight">
            <span class="status-label">Outstanding Balance:</span>
            <span class="status-value outstanding">₹{{ invoice.remaining_due }}</span>
        </div>
        
        <div class="status-item">
            <span class="status-label">Due Date:</span>
            <span class="status-value">{{ invoice.due_date|date:"F j, Y" }}</span>
        </div>
        
        <div class="status-item">
            <span class="status-label">Days Overdue:</span>
            <span class="status-value overdue">{{ days_overdue }} days</span>
        </div>
    </div>
</div>

<!-- Payment Progress -->
<div class="payment-progress">
    <h3 class="section-title">📊 Payment Progress</h3>
    <div class="progress-container">
        <div class="progress-bar">
            <div class="progress-fill" style="width: {{ invoice.get_payment_percentage }}%"></div>
        </div>
        <div class="progress-text">
            {{ invoice.get_payment_percentage }}% paid (₹{{ invoice.total_paid }} of ₹{{ invoice.total_amount }})
        </div>
    </div>
</div>

<!-- Payment History -->
{% if payments %}
<div class="payment-history">
    <h3 class="section-title">📋 Payment History</h3>
    <div class="history-list">
        {% for payment in payments %}
        <div class="history-item">
            <div class="history-date">{{ payment.payment_date|date:"M j, Y" }}</div>
            <div class="history-amount">₹{{ payment.amount_paid }}</div>
            <div class="history-mode">{{ payment.payment_mode }}</div>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<!-- Urgent Payment Action -->
<div class="urgent-payment-action">
    <div class="action-header">
        <h3 class="action-title">
            <i class="fas fa-bolt"></i> Immediate Action Required
        </h3>
        <p class="action-subtitle">
            Please settle your outstanding balance of <strong>₹{{ invoice.remaining_due }}</strong> immediately to avoid service interruption.
        </p>
    </div>

    <div class="action-buttons">
        <a href="{% url 'invoice_student' invoice.slug %}" class="btn-primary-action">
            <i class="fas fa-file-invoice"></i>
            View Full Invoice
        </a>
        <a href="tel:{{ librarian.librarian_phone_num }}" class="btn-secondary-action">
            <i class="fas fa-phone"></i>
            Call Library
        </a>
    </div>
</div>

<!-- Payment Options -->
<div class="payment-options">
    <h3 class="section-title">💰 How to Pay</h3>
    <div class="options-grid">
        <div class="option-card">
            <div class="option-icon">💳</div>
            <h4>Online Payment</h4>
            <p>Pay securely online using UPI, cards, or net banking</p>
        </div>
        
        <div class="option-card">
            <div class="option-icon">🏦</div>
            <h4>Bank Transfer</h4>
            <p>Transfer directly to our bank account</p>
        </div>
        
        <div class="option-card">
            <div class="option-icon">🏢</div>
            <h4>Visit Library</h4>
            <p>Pay in person at our library counter</p>
        </div>
        
        <div class="option-card">
            <div class="option-icon">📱</div>
            <h4>UPI Payment</h4>
            <p>Quick payment using any UPI app</p>
        </div>
    </div>
</div>

<!-- Contact Information -->
<div class="contact-section">
    <h3 class="section-title">📞 Need Help?</h3>
    <p>If you have any questions about your payment or need assistance, please contact us:</p>
    <div class="contact-details">
        <div class="contact-item">
            <i class="fas fa-envelope"></i>
            <span><EMAIL></span>
        </div>
        <div class="contact-item">
            <i class="fas fa-phone"></i>
            <span>{{ librarian.librarian_phone_num }}</span>
        </div>
        <div class="contact-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>{{ library_name }}</span>
        </div>
    </div>
</div>

<!-- Action Button -->
<div class="action-section">
    <a href="#" class="pay-now-button">
        <i class="fas fa-credit-card"></i>
        Pay Now - ₹{{ invoice.remaining_due }}
    </a>
    <p class="action-note">Click above to make your payment securely online</p>
</div>

<p class="closing-message">
    Thank you for your prompt attention to this matter. We appreciate your business and look forward to continuing to serve you!
</p>
{% endblock %}

{% block extra_css %}
<style>
    .reminder-alert {
        background: linear-gradient(135deg, #fef3c7, #fde68a);
        border: 2px solid #f59e0b;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .alert-icon {
        font-size: 2.5rem;
        color: #d97706;
    }

    .alert-content {
        flex: 1;
    }

    .alert-title {
        color: #92400e;
        margin: 0 0 0.5rem 0;
        font-size: 1.25rem;
        font-weight: 700;
    }

    .alert-message {
        color: #78350f;
        margin: 0;
        font-weight: 500;
    }

    .payment-status {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .status-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        background: white;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
    }

    .status-item.highlight {
        background: linear-gradient(135deg, #fee2e2, #fecaca);
        border-color: #ef4444;
    }

    .status-label {
        font-weight: 500;
        color: #6b7280;
    }

    .status-value {
        font-weight: 600;
        color: #1f2937;
    }

    .status-value.paid {
        color: #059669;
    }

    .status-value.outstanding {
        color: #dc2626;
        font-size: 1.1rem;
    }

    .status-value.overdue {
        color: #dc2626;
        font-weight: 700;
    }

    .payment-progress {
        background: #f1f5f9;
        border: 1px solid #cbd5e1;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .progress-container {
        margin-top: 1rem;
    }

    .progress-bar {
        width: 100%;
        height: 12px;
        background-color: #e5e7eb;
        border-radius: 6px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #10b981, #059669);
        transition: width 0.3s ease;
    }

    .progress-text {
        text-align: center;
        font-weight: 500;
        color: #374151;
    }

    .payment-history {
        background: #fefefe;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .history-list {
        margin-top: 1rem;
    }

    .history-item {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 1rem;
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
        align-items: center;
    }

    .history-item:last-child {
        border-bottom: none;
    }

    .history-date {
        font-weight: 500;
        color: #6b7280;
    }

    .history-amount {
        font-weight: 600;
        color: #059669;
        text-align: center;
    }

    .history-mode {
        font-weight: 500;
        color: #374151;
        text-align: right;
    }

    .payment-options {
        background: linear-gradient(135deg, #dbeafe, #bfdbfe);
        border: 1px solid #3b82f6;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .options-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-top: 1rem;
    }

    .option-card {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
        border: 1px solid #e5e7eb;
    }

    .option-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .option-card h4 {
        margin: 0.5rem 0;
        color: #1f2937;
        font-size: 1rem;
    }

    .option-card p {
        margin: 0;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .contact-section {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .contact-details {
        margin-top: 1rem;
    }

    .contact-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin: 0.75rem 0;
        color: #374151;
    }

    .contact-item i {
        color: #6366f1;
        width: 20px;
    }

    .action-section {
        text-align: center;
        margin: 2rem 0;
    }

    .pay-now-button {
        display: inline-block;
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 1rem 2rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        font-size: 1.1rem;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        transition: all 0.3s ease;
    }

    .pay-now-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        color: white;
        text-decoration: none;
    }

    .action-note {
        margin-top: 1rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .closing-message {
        text-align: center;
        font-style: italic;
        color: #6b7280;
        margin-top: 2rem;
        padding: 1rem;
        background: #f9fafb;
        border-radius: 8px;
    }

    /* Payment Highlight Schema Styles */
    .payment-highlight-schema {
        background: linear-gradient(135deg, #fef3c7, #fde68a);
        border: 3px solid #f59e0b;
        border-radius: 20px;
        padding: 2rem;
        margin: 2rem 0;
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
    }

    .schema-title {
        text-align: center;
        color: #92400e;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .highlight-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .highlight-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
        border: 2px solid transparent;
    }

    .highlight-card:hover {
        transform: translateY(-5px);
    }

    .highlight-card.pending-payment {
        border-color: #ef4444;
        background: linear-gradient(135deg, #fee2e2, #fecaca);
    }

    .highlight-card.amount-due {
        border-color: #3b82f6;
        background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    }

    .highlight-card.months-covered {
        border-color: #10b981;
        background: linear-gradient(135deg, #d1fae5, #a7f3d0);
    }

    .highlight-card.due-date {
        border-color: #f59e0b;
        background: linear-gradient(135deg, #fef3c7, #fde68a);
    }

    .card-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .pending-payment .card-icon {
        color: #dc2626;
    }

    .amount-due .card-icon {
        color: #2563eb;
    }

    .months-covered .card-icon {
        color: #059669;
    }

    .due-date .card-icon {
        color: #d97706;
    }

    .card-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .card-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .card-subtitle {
        font-size: 0.75rem;
        color: #6b7280;
        font-weight: 500;
    }

    .overdue-text {
        color: #dc2626;
        font-weight: 600;
        text-transform: uppercase;
    }

    .payment-breakdown-summary {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 12px;
        padding: 1.5rem;
        border: 2px solid #f59e0b;
    }

    .breakdown-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .breakdown-row:last-child {
        border-bottom: none;
    }

    .breakdown-label {
        font-weight: 500;
        color: #374151;
    }

    .breakdown-value {
        font-weight: 600;
        color: #1f2937;
    }

    .breakdown-row.paid .breakdown-value {
        color: #059669;
    }

    .breakdown-row.pending .breakdown-value {
        color: #dc2626;
        font-size: 1.1rem;
    }

    .breakdown-row.progress .breakdown-value {
        color: #2563eb;
    }

    .breakdown-value.highlight {
        background: #fee2e2;
        padding: 0.25rem 0.75rem;
        border-radius: 6px;
        border: 1px solid #fecaca;
    }

    /* Urgent Payment Action Styles */
    .urgent-payment-action {
        background: linear-gradient(135deg, #fee2e2, #fecaca);
        border: 3px solid #dc2626;
        border-radius: 20px;
        padding: 2rem;
        margin: 2rem 0;
        text-align: center;
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
        animation: pulse-border 2s infinite;
    }

    @keyframes pulse-border {
        0% { border-color: #dc2626; }
        50% { border-color: #ef4444; }
        100% { border-color: #dc2626; }
    }

    .action-header {
        margin-bottom: 2rem;
    }

    .action-title {
        color: #991b1b;
        font-size: 1.75rem;
        font-weight: 800;
        margin-bottom: 1rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .action-subtitle {
        color: #7f1d1d;
        font-size: 1.1rem;
        font-weight: 600;
        line-height: 1.5;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn-primary-action,
    .btn-secondary-action {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem 2rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 700;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-primary-action {
        background: linear-gradient(135deg, #dc2626, #991b1b);
        color: white;
        border: 2px solid #dc2626;
    }

    .btn-primary-action:hover {
        background: linear-gradient(135deg, #991b1b, #7f1d1d);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
        color: white;
    }

    .btn-secondary-action {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
        border: 2px solid #f59e0b;
    }

    .btn-secondary-action:hover {
        background: linear-gradient(135deg, #d97706, #b45309);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
        color: white;
    }

    @media only screen and (max-width: 600px) {
        .status-grid,
        .options-grid {
            grid-template-columns: 1fr;
        }

        .reminder-alert {
            flex-direction: column;
            text-align: center;
        }

        .history-item {
            grid-template-columns: 1fr;
            text-align: center;
        }

        .highlight-grid {
            grid-template-columns: 1fr;
        }

        .payment-highlight-schema {
            padding: 1.5rem;
        }

        .schema-title {
            font-size: 1.25rem;
        }

        .card-value {
            font-size: 1.25rem;
        }

        .breakdown-row {
            flex-direction: column;
            text-align: center;
            gap: 0.5rem;
        }

        .urgent-payment-action {
            padding: 1.5rem;
        }

        .action-title {
            font-size: 1.5rem;
        }

        .action-subtitle {
            font-size: 1rem;
        }

        .action-buttons {
            flex-direction: column;
            align-items: center;
        }

        .btn-primary-action,
        .btn-secondary-action {
            width: 100%;
            max-width: 280px;
            justify-content: center;
        }
    }
</style>
{% endblock %}
